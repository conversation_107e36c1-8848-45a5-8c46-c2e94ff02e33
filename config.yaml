# 自考成绩查询自动化系统配置文件
# 此文件为示例配置，您可以根据需要修改相应的配置项

config:
  # 网站配置
  login_url: "https://121.204.170.198:8082/zk/reg/v3/?rlx=1752850109&p=FIXED06FA3SKREG"
  
  # 浏览器配置
  headless: false              # 是否无头模式运行
  slow_mo: 500                # 操作间隔(毫秒)
  page_timeout: 30000         # 页面超时(毫秒)
  navigation_timeout: 20000   # 导航超时(毫秒)
  element_timeout: 10000      # 元素等待超时(毫秒)
  
  # 重试配置
  max_retries: 3              # 最大重试次数
  retry_delay: 2.0            # 重试间隔(秒)
  captcha_retries: 3          # 验证码重试次数
  
  # 目录配置
  screenshot_dir: "output/screenshots"
  log_dir: "output/logs"
  data_file: "data/students.csv"
  
  # 日志配置
  log_level: "INFO"           # 日志级别: DEBUG, INFO, WARNING, ERROR
  log_rotation: "10 MB"       # 日志轮转大小
  log_retention: "7 days"     # 日志保留时间
  
  # 验证码配置
  captcha_debug: true         # 验证码调试模式
  captcha_save_path: "../output/logs/captcha_debug"
  
  # 截图配置
  screenshot_quality: 90      # 截图质量 (1-100)
  screenshot_format: "png"    # 截图格式
  viewport_width: 1920        # 视口宽度
  viewport_height: 1080       # 视口高度

# 选择器配置（可选，用于自定义页面元素选择器）
selectors:
  # 登录页面选择器
  username_input:
    - "input[name='myname']"
    - "#myname"
    - "input[name='username']"
    - "input[id='username']"
    - "#username"
    - "input[placeholder*='身份证']"
    - "input[placeholder*='用户名']"
  
  password_input:
    - "input[name='mypwd']"
    - "#mypwd"
    - "input[name='password']"
    - "input[id='password']"
    - "#password"
    - "input[type='password']"
  
  captcha_input:
    - "input[name='verifycode']"
    - "#verifycode"
    - "input[name='captcha']"
    - "input[id='captcha']"
    - "#captcha"
    - "input[placeholder*='验证码']"

  captcha_image:
    - "#seccodeimg"
    - "img[src*='captcha']"
    - "img[alt*='验证码']"
    - "#captcha_img"
    - ".captcha-img"

  login_button:
    - "#loginBtn"
    - ".loginBtn"
    - "button[type='submit']"
    - "input[type='submit']"
    - ".login-btn"
  
  # 导航选择器
  score_query_menu:
    - "text=当次成绩查询"
    - "//a[contains(text(), '当次成绩查询')]"
    - "//td[contains(text(), '当次成绩查询')]"
    - "//span[contains(text(), '当次成绩查询')]"
    - "[title*='当次成绩查询']"
    - "[alt*='当次成绩查询']"

  query_options:
    - "text=点击进入"
    - "//a[contains(text(), '点击进入')]"
    - "//button[contains(text(), '点击进入')]"
    - "//input[contains(@value, '点击进入')]"
    - "[title*='点击进入']"
    - "[alt*='点击进入']"

  menu_items:
    - "text=当次成绩查询"
    - "//a[contains(text(), '当次成绩查询')]"
    - "[title*='成绩查询']"
    - "//li[contains(text(), '当次成绩查询')]"

  # 成绩页面选择器
  score_table:
    - "table"
    - ".score-table"
    - "#score_table"
    - "[class*='table']"

  # 密码修改页面选择器
  password_change_indicators:
    - "text=修改密码"
    - "text=密码修改"
    - "text=修改登录密码"
    - "text=密码即将过期"
    - "text=请修改密码"
    - "//text()[contains(., '修改密码')]"
    - "//text()[contains(., '密码修改')]"
    - "//text()[contains(., '修改登录密码')]"
    - "//text()[contains(., '密码即将过期')]"
    - "//text()[contains(., '请修改密码')]"
    - "//div[contains(text(), '修改密码')]"
    - "//span[contains(text(), '修改密码')]"
    - "//h1[contains(text(), '修改密码')]"
    - "//h2[contains(text(), '修改密码')]"
    - "//h3[contains(text(), '修改密码')]"
    - "[title*='修改密码']"
    - "[alt*='修改密码']"

  old_password_input:
    - "input[name='oldpwd']"
    - "input[name='old_password']"
    - "input[name='oldPassword']"
    - "input[id='oldpwd']"
    - "input[id='old_password']"
    - "input[id='oldPassword']"
    - "#oldpwd"
    - "#old_password"
    - "#oldPassword"
    - "input[placeholder*='原密码']"
    - "input[placeholder*='旧密码']"
    - "input[placeholder*='当前密码']"
    - "input[placeholder*='输入原密码']"
    - "input[type='password'][name*='old']"
    - "input[type='password'][id*='old']"

  new_password_input:
    - "#mypwd"                      # 实际页面使用的ID
    - "input[name='mypwd']"         # 实际页面使用的name
    - "input[id='mypwd']"           # 备用选择器
    - "input[name='newpwd']"
    - "input[name='new_password']"
    - "input[name='newPassword']"
    - "input[id='newpwd']"
    - "input[id='new_password']"
    - "input[id='newPassword']"
    - "#newpwd"
    - "#new_password"
    - "#newPassword"
    - "input[placeholder*='新密码']"
    - "input[placeholder*='输入新密码']"
    - "input[placeholder*='请输入新密码']"
    - "input[type='password'][name*='new']"
    - "input[type='password'][id*='new']"

  confirm_password_input:
    - "#mypwd2"                     # 实际页面使用的ID
    - "input[name='mypwd2']"        # 实际页面使用的name
    - "input[id='mypwd2']"          # 备用选择器
    - "input[name='confirmpwd']"
    - "input[name='confirm_password']"
    - "input[name='confirmPassword']"
    - "input[name='repwd']"
    - "input[name='re_password']"
    - "input[id='confirmpwd']"
    - "input[id='confirm_password']"
    - "input[id='confirmPassword']"
    - "input[id='repwd']"
    - "input[id='re_password']"
    - "#confirmpwd"
    - "#confirm_password"
    - "#confirmPassword"
    - "#repwd"
    - "#re_password"
    - "input[placeholder*='确认密码']"
    - "input[placeholder*='再次输入']"
    - "input[placeholder*='重复密码']"
    - "input[placeholder*='再次输入密码']"
    - "input[type='password'][name*='confirm']"
    - "input[type='password'][id*='confirm']"
    - "input[type='password'][name*='re']"
    - "input[type='password'][id*='re']"

  change_password_button:
    - "#btnsubmit"                  # 实际页面使用的ID
    - "input[id='btnsubmit']"       # 备用选择器
    - "input[value='修改登录密码']"   # 实际页面使用的value
    - "input[value*='修改密码']"
    - "input[value*='修改登录密码']"
    - "input[value*='确认修改']"
    - "input[value*='提交']"
    - "button:has-text('修改密码')"
    - "button:has-text('修改登录密码')"
    - "button:has-text('确认修改')"
    - "button:has-text('提交')"
    - "//button[contains(text(), '修改密码')]"
    - "//button[contains(text(), '修改登录密码')]"
    - "//button[contains(text(), '确认修改')]"
    - "//input[contains(@value, '修改密码')]"
    - "//input[contains(@value, '修改登录密码')]"
    - "//input[contains(@value, '确认修改')]"
    - "#changePasswordBtn"
    - "#modifyPasswordBtn"
    - ".change-password-btn"
    - ".modify-password-btn"
    - "[onclick*='changePassword']"
    - "[onclick*='modifyPassword']"
    - "[onclick*='AjaxP']"          # 实际页面使用的onclick函数
