# 🚀 MCP-Chrome 完整部署教程 - 保姆级指南

## 📋 概述

本教程将手把手教您在任何Windows电脑上安装和配置 MCP-Chrome，实现浏览器自动化功能，并完美集成到VS Code的MCP工作区中。

**完成后您将获得：**
- ✅ 20+浏览器自动化工具
- ✅ AI可以控制您的Chrome浏览器
- ✅ 网页截图、内容分析、书签管理等功能
- ✅ 与VS Code MCP生态完美集成

---

## 🎯 安装前准备

### 系统要求
- ✅ Windows 10/11
- ✅ Chrome/Chromium 浏览器
- ✅ Node.js 18+ 
- ✅ VS Code (推荐安装 Cursor 或 支持MCP的IDE)

### 检查环境
```powershell
# 检查Node.js版本（必须18+）
node --version

# 检查npm版本
npm --version

# 检查Chrome是否安装
Get-Process chrome -ErrorAction SilentlyContinue
```

---

## 📁 第一步：下载项目文件

### 方法一：从GitHub Release下载（推荐）

1. **访问GitHub Release页面**
   ```
   https://github.com/hangwin/mcp-chrome/releases
   ```

2. **下载最新版本的扩展包**
   - 下载 `chrome-extension.zip`
   - 解压到您的工作目录，比如：`d:\mcp-chrome\`

### 方法二：克隆完整项目（开发者推荐）

```powershell
# 创建工作目录
mkdir d:\mcp-chrome
cd d:\mcp-chrome

# 克隆项目
git clone https://github.com/hangwin/mcp-chrome.git .

# 安装依赖
npm install -g pnpm
pnpm install
```

---

## 🔧 第二步：安装Native Messaging桥接

### 全局安装mcp-chrome-bridge

```powershell
# 使用npm安装（推荐）
npm install -g mcp-chrome-bridge

# 或使用pnpm安装
pnpm install -g mcp-chrome-bridge --unsafe-perm
```

### 验证安装成功

```powershell
# 检查版本（确保是最新版本 1.0.14+）
mcp-chrome-bridge -v

# 检查Native Messaging注册
# 应该在以下路径找到配置文件：
dir "C:\Users\<USER>\AppData\Roaming\Google\Chrome\NativeMessagingHosts\com.chromemcp.nativehost.json"
```

### 🔍 故障排除 - 安装问题

如果安装失败，请尝试：

```powershell
# 清理npm缓存
npm cache clean --force

# 使用管理员权限重新安装
# 右键点击PowerShell -> 以管理员身份运行
npm install -g mcp-chrome-bridge --force

# 检查安装位置
npm list -g mcp-chrome-bridge
```

---

## 🌐 第三步：配置Chrome扩展

### 1. 启用Chrome开发者模式

1. **打开Chrome扩展管理页面**
   ```
   chrome://extensions/
   ```

2. **启用开发者模式**
   - 右上角找到"开发者模式"开关
   - 点击开关，启用开发者模式

### 2. 加载Chrome扩展

1. **点击"加载已解压的扩展程序"**

2. **选择扩展文件夹**
   ```
   # 如果您下载的是release版本
   d:\mcp-chrome\extension\
   
   # 如果您克隆了完整项目
   d:\mcp-chrome\extension\
   ```

3. **验证扩展安装**
   - 扩展列表中出现"chrome-mcp-server"
   - 扩展状态为"已启用"
   - 记录扩展ID（类似：`hbdgbgagpkpjffpklnamcljpakneikee`）

### 3. 测试扩展连接

1. **点击扩展图标**
   - 在Chrome工具栏找到mcp-chrome图标
   - 点击图标打开扩展面板

2. **建立连接**
   - 点击"Connect"按钮
   - 查看连接状态显示为"已连接"
   - 确认显示端口号：12306

### 🔍 故障排除 - 扩展问题

如果扩展连接失败：

```powershell
# 1. 检查bridge是否正确安装
mcp-chrome-bridge -v

# 2. 检查日志文件
# 查看安装路径下的logs文件夹
# 通常在：C:\Users\<USER>\AppData\Local\nvm\版本\node_modules\mcp-chrome-bridge\dist\logs\

# 3. 重启Chrome浏览器
taskkill /F /IM chrome.exe
start chrome
```

---

## ⚙️ 第四步：配置MCP连接

### 1. 创建MCP配置文件

#### 对于Cursor用户：
```powershell
# 配置文件路径
$configPath = "$env:APPDATA\.cursor\mcp.json"

# 创建配置目录（如果不存在）
$configDir = Split-Path $configPath
if (!(Test-Path $configDir)) {
    New-Item -ItemType Directory -Path $configDir -Force
}
```

#### 对于其他MCP客户端：
根据您的客户端查找相应的配置文件位置。

### 2. 更新MCP配置

#### 添加Chrome MCP配置（推荐StreamableHTTP方式）

```json
{
  "mcpServers": {
    "chrome-mcp-server": {
      "type": "streamableHttp",
      "url": "http://127.0.0.1:12306/mcp"
    }
  }
}
```

#### 备选STDIO方式配置

首先查找安装路径：
```powershell
npm list -g mcp-chrome-bridge
```

然后配置：
```json
{
  "mcpServers": {
    "chrome-mcp-stdio": {
      "command": "node",
      "args": [
        "C:\\Users\\<USER>\\AppData\\Roaming\\npm\\node_modules\\mcp-chrome-bridge\\dist\\mcp\\mcp-server-stdio.js"
      ]
    }
  }
}
```

### 3. 验证MCP连接

1. **重启您的MCP客户端**（如Cursor、VS Code等）

2. **测试连接**
   - 在聊天界面输入：`@workspace 列出可用的MCP工具`
   - 应该能看到20+个浏览器自动化工具

---

## 🎨 第五步：VS Code工作区集成

### 1. 创建工作区配置文件

```json
{
  "folders": [
    {
      "name": "🌐 Chrome MCP Server",
      "path": "d:\\mcp-chrome"
    }
  ],
  "settings": {
    "terminal.integrated.defaultProfile.windows": "PowerShell"
  },
  "tasks": {
    "version": "2.0.0",
    "tasks": [
      {
        "label": "🌐 Start Chrome MCP Server",
        "type": "shell",
        "command": "mcp-chrome-bridge",
        "args": ["--port", "12306"],
        "group": "build",
        "presentation": {
          "echo": true,
          "reveal": "always",
          "focus": false,
          "panel": "new"
        },
        "problemMatcher": []
      },
      {
        "label": "🌐 Test Chrome Extension",
        "type": "shell",
        "command": "start",
        "args": ["chrome://extensions/"],
        "group": "test",
        "presentation": {
          "echo": true,
          "reveal": "always",
          "focus": false,
          "panel": "new"
        }
      },
      {
        "label": "🌐 Open Chrome MCP Extension Directory",
        "type": "shell",
        "command": "explorer",
        "args": ["d:\\mcp-chrome\\extension"],
        "group": "build",
        "presentation": {
          "echo": true,
          "reveal": "always",
          "focus": false,
          "panel": "new"
        }
      }
    ]
  },
  "extensions": {
    "recommendations": [
      "ms-vscode.vscode-typescript-next",
      "bradlc.vscode-tailwindcss",
      "esbenp.prettier-vscode"
    ]
  }
}
```

### 2. 保存工作区文件

```powershell
# 保存为 chrome-mcp.code-workspace
$workspaceContent = @'
{工作区JSON内容}
'@

$workspaceContent | Out-File -FilePath "d:\mcp-chrome\chrome-mcp.code-workspace" -Encoding UTF8
```

---

## 🧪 第六步：功能测试验证

### 1. 基础连接测试

```powershell
# 检查服务器是否运行
netstat -an | findstr :12306

# 应该显示：
# TCP    127.0.0.1:12306        0.0.0.0:0              LISTENING
```

### 2. Chrome扩展测试

1. **访问扩展页面**
   ```
   chrome://extensions/
   ```

2. **检查扩展状态**
   - 扩展显示为"已启用"
   - 点击扩展图标显示"服务运行中（端口：12306）"

### 3. MCP工具测试

在您的MCP客户端中测试以下功能：

```
测试截图功能：
@workspace 请截图当前Chrome页面

测试网页导航：
@workspace 打开GitHub主页并截图

测试书签管理：
@workspace 将当前页面添加到书签

测试窗口管理：
@workspace 列出所有打开的Chrome标签页
```

---

## 🔧 故障排除指南

### 常见问题1：扩展连接失败

**症状**：扩展显示"连接失败"或"未连接"

**解决方案**：
```powershell
# 1. 检查bridge安装
mcp-chrome-bridge -v

# 2. 检查Native Messaging注册
reg query "HKCU\Software\Google\Chrome\NativeMessagingHosts" /s

# 3. 重新安装bridge
npm uninstall -g mcp-chrome-bridge
npm install -g mcp-chrome-bridge

# 4. 重启Chrome
taskkill /F /IM chrome.exe
start chrome
```

### 常见问题2：MCP连接失败

**症状**：MCP客户端显示Chrome工具不可用

**解决方案**：
```powershell
# 1. 检查端口占用
netstat -an | findstr :12306

# 2. 检查防火墙设置
# Windows防火墙 -> 允许应用通过防火墙 -> 添加Chrome和Node.js

# 3. 重启MCP服务
# 停止所有相关进程，重新启动
```

### 常见问题3：权限问题

**症状**：安装或运行时出现权限错误

**解决方案**：
```powershell
# 以管理员身份运行PowerShell
Start-Process PowerShell -Verb RunAs

# 重新执行安装命令
npm install -g mcp-chrome-bridge --force
```

---

## 📚 高级配置选项

### 1. 自定义端口配置

如果端口12306被占用，可以修改配置：

```json
{
  "mcpServers": {
    "chrome-mcp-server": {
      "type": "streamableHttp",
      "url": "http://127.0.0.1:12307/mcp"
    }
  }
}
```

### 2. 多浏览器支持

如果您使用多个Chrome实例：

```json
{
  "mcpServers": {
    "chrome-mcp-primary": {
      "type": "streamableHttp",
      "url": "http://127.0.0.1:12306/mcp"
    },
    "chrome-mcp-secondary": {
      "type": "streamableHttp",
      "url": "http://127.0.0.1:12307/mcp"
    }
  }
}
```

### 3. 自动启动配置

创建启动脚本：

```powershell
# 创建启动脚本 start-chrome-mcp.ps1
@'
# 启动Chrome MCP服务器
Write-Host "启动Chrome MCP服务器..." -ForegroundColor Green

# 检查Chrome是否运行
$chromeProcess = Get-Process chrome -ErrorAction SilentlyContinue
if (!$chromeProcess) {
    Write-Host "启动Chrome浏览器..." -ForegroundColor Yellow
    Start-Process chrome
    Start-Sleep 3
}

# 启动MCP桥接服务
Write-Host "启动MCP桥接服务..." -ForegroundColor Yellow
Start-Process "mcp-chrome-bridge" -WindowStyle Hidden

Write-Host "Chrome MCP服务器启动完成！" -ForegroundColor Green
Write-Host "访问 http://127.0.0.1:12306/mcp 检查服务状态" -ForegroundColor Cyan
'@ | Out-File -FilePath "d:\mcp-chrome\start-chrome-mcp.ps1" -Encoding UTF8
```

---

## ✅ 部署验证清单

### 安装验证
- [ ] Node.js 18+ 已安装
- [ ] Chrome浏览器已安装
- [ ] mcp-chrome-bridge全局安装成功
- [ ] Native Messaging注册表项已创建

### 扩展验证  
- [ ] Chrome扩展成功加载
- [ ] 扩展状态显示"已启用"
- [ ] 扩展连接显示"已连接"
- [ ] 服务端口12306正常监听

### MCP验证
- [ ] MCP配置文件已正确设置
- [ ] MCP客户端能识别Chrome工具
- [ ] 基础功能测试通过（截图、导航等）
- [ ] VS Code工作区集成正常

### 功能验证
- [ ] 网页截图功能正常
- [ ] 页面导航功能正常  
- [ ] 书签管理功能正常
- [ ] 窗口管理功能正常
- [ ] 网络监控功能正常

---

## 🎯 使用建议

### 最佳实践
1. **定期更新**：保持mcp-chrome-bridge为最新版本
2. **备份配置**：定期备份MCP配置文件
3. **性能监控**：监控浏览器内存使用情况
4. **安全设置**：只在可信任的网站使用自动化功能

### 推荐使用场景
- 🔍 **网页数据采集**：自动收集网页信息
- 📊 **网站测试**：自动化UI测试
- 📚 **学习辅助**：自动收集学习资料
- 💼 **工作自动化**：重复性网页操作自动化

---

## 📞 获取帮助

### 官方资源
- **GitHub仓库**：https://github.com/hangwin/mcp-chrome
- **文档中心**：查看项目docs目录
- **Issue提交**：GitHub Issues页面

### 社区支持
- **微信交流群**：扫描README中的二维码
- **技术论坛**：相关MCP社区

### 常用命令参考

```powershell
# 检查服务状态
netstat -an | findstr :12306

# 重启Chrome MCP服务
taskkill /F /IM chrome.exe
taskkill /F /IM node.exe
mcp-chrome-bridge

# 查看扩展状态
start chrome://extensions/

# 检查MCP配置
Get-Content "$env:APPDATA\.cursor\mcp.json"
```

---

## 🎉 恭喜完成！

您已成功完成Chrome MCP的完整部署！现在您可以：

- ✅ 让AI控制您的Chrome浏览器
- ✅ 自动化网页操作和数据采集
- ✅ 享受20+浏览器自动化工具
- ✅ 与其他MCP服务器协作工作

**开始您的AI浏览器自动化之旅吧！** 🚀

---

*教程版本：v1.0*  
*更新时间：2025年7月3日*  
*适用版本：mcp-chrome-bridge 1.0.14+*
