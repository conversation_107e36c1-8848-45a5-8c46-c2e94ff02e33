{"mcp": {"servers": {"playwright-mcp": {"type": "stdio", "command": "npx", "args": ["-y", "@executeautomation/playwright-mcp-server"]}, "puppeteer-mcp": {"type": "stdio", "command": "npx", "args": ["-y", "@executeautomation/puppeteer-mcp-server"]}, "firecrawl-mcp": {"type": "stdio", "command": "npx", "args": ["-y", "@mendable/firecrawl-mcp-server"], "env": {"FIRECRAWL_API_KEY": "your-firecrawl-api-key"}}, "amap-maps": {"type": "stdio", "command": "npx", "args": ["-y", "@amap/amap-maps-mcp-server"], "env": {"AMAP_MAPS_API_KEY": "6675b7a3050e70c69bab67324fd9a891"}}, "context7": {"type": "stdio", "command": "npx", "args": ["-y", "@upstash/context7-mcp@latest"]}, "sequential-thinking": {"type": "stdio", "command": "E:/mcp/.venv/Scripts/python.exe", "args": ["-m", "mcp_sequential_thinking.server"]}, "shrimp-task-manager": {"type": "stdio", "command": "node", "args": ["E:/mcp/mcp-shrimp-task-manager/dist/index.js"], "env": {"DATA_DIR": "E:/mcp/shrimp-data", "TEMPLATES_USE": "en", "ENABLE_GUI": "false"}}, "mem0-mcp-bridge": {"type": "stdio", "command": "E:/mcp/.venv/Scripts/python.exe", "args": ["E:/mcp/mem0-mcp-integration/mem0_mcp_server.py"], "env": {"PYTHONPATH": "E:/mcp/mem0-mcp-integration;E:/mcp", "MEM0_TELEMETRY": "false", "PYTHONIOENCODING": "utf-8"}}, "chrome-mcp-server": {"type": "stdio", "command": "E:/mcp/.venv/Scripts/python.exe", "args": ["E:/mcp/chrome-mcp-stdio-wrapper.py"]}}}, "github.copilot.enable": {"*": true}, "explorer.confirmDelete": false}