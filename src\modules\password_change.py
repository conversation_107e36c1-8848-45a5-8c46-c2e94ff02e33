#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
密码修改处理模块
实现密码修改页面的检测、处理和验证功能
"""

import time
from typing import Tu<PERSON>, Optional
from playwright.sync_api import Page

import sys
sys.path.append('..')
from config import CONFIG, SELECTORS
from modules.logger import log_info, log_success, log_error, log_warning


class PasswordChangeHandler:
    """密码修改处理器"""

    def __init__(self):
        """初始化密码修改处理器"""
        self.max_retries = CONFIG.get('max_retries', 3)
        self.retry_delay = CONFIG.get('retry_delay', 2.0)
        self.page_timeout = CONFIG.get('page_timeout', 30000)
        self.element_timeout = CONFIG.get('element_timeout', 10000)

    def detect_password_change_page(self, page: Page) -> Tuple[bool, str]:
        """
        检测当前页面是否为密码修改页面

        Args:
            page: Playwright页面对象

        Returns:
            (是否检测到密码修改页面, 检测结果描述)
        """
        log_info("开始检测密码修改页面", "密码修改")
        
        try:
            # 等待页面稳定
            page.wait_for_load_state("networkidle", timeout=self.page_timeout)
            time.sleep(1)
            
            current_url = page.url
            page_title = page.title()
            page_content = page.content()
            
            log_info(f"检测页面URL: {current_url}", "密码修改")
            log_info(f"检测页面标题: {page_title}", "密码修改")
            
            # 使用配置的选择器检测密码修改指示器
            password_change_selectors = SELECTORS.get('password_change_indicators', [])
            
            for selector in password_change_selectors:
                try:
                    # 检查选择器是否匹配
                    if selector.startswith('text=') or selector.startswith('//text()'):
                        # 文本选择器，检查页面内容
                        text_to_find = selector.replace('text=', '').replace('//text()[contains(., \'', '').replace('\')]', '').replace('\'', '')
                        if text_to_find in page_content:
                            log_success(f"检测到密码修改页面，匹配文本: {text_to_find}", "密码修改")
                            return True, f"检测到密码修改页面，匹配文本: {text_to_find}"
                    else:
                        # 元素选择器，检查元素是否存在
                        element = page.query_selector(selector)
                        if element:
                            element_text = element.text_content() or ""
                            log_success(f"检测到密码修改页面，匹配选择器: {selector}", "密码修改")
                            return True, f"检测到密码修改页面，匹配选择器: {selector}"
                            
                except Exception as e:
                    # 单个选择器失败不影响整体检测
                    log_warning(f"选择器检测失败: {selector} - {str(e)}", "密码修改")
                    continue
            
            # 额外检查页面标题和URL
            password_keywords = ['修改密码', '密码修改', '修改登录密码', '密码即将过期', '请修改密码']
            for keyword in password_keywords:
                if keyword in page_title or keyword in current_url:
                    log_success(f"检测到密码修改页面，匹配关键词: {keyword}", "密码修改")
                    return True, f"检测到密码修改页面，匹配关键词: {keyword}"
            
            log_info("未检测到密码修改页面", "密码修改")
            return False, "未检测到密码修改页面"
            
        except Exception as e:
            log_error("密码修改", e)
            return False, f"检测密码修改页面异常: {str(e)}"

    def handle_password_change(self, page: Page, original_password: str) -> Tuple[bool, str]:
        """
        处理密码修改流程

        Args:
            page: Playwright页面对象
            original_password: 原密码

        Returns:
            (是否处理成功, 处理结果描述)
        """
        return self.handle_password_change_with_retry(page, original_password, self.max_retries)

    def handle_password_change_with_retry(self, page: Page, original_password: str, max_retries: int = 3) -> Tuple[bool, str]:
        """
        带重试的密码修改处理流程

        Args:
            page: Playwright页面对象
            original_password: 原密码
            max_retries: 最大重试次数

        Returns:
            (是否处理成功, 处理结果描述)
        """
        log_info(f"开始处理密码修改流程，最大重试次数: {max_retries}", "密码修改")

        for attempt in range(max_retries):
            log_info(f"密码修改尝试 {attempt + 1}/{max_retries}", "密码修改")

            try:
                success, error_msg = self._attempt_password_change(page, original_password)

                if success:
                    log_success(f"密码修改成功，尝试次数: {attempt + 1}", "密码修改")
                    return True, f"密码修改成功，尝试次数: {attempt + 1}"

                log_warning(f"密码修改失败: {error_msg}", "密码修改")

                # 如果不是最后一次尝试，准备重试
                if attempt < max_retries - 1:
                    log_info("准备重试密码修改", "密码修改")

                    # 等待一段时间后重试
                    time.sleep(self.retry_delay)

                    # 尝试刷新页面或重新检测
                    try:
                        page.wait_for_load_state("networkidle", timeout=5000)
                        time.sleep(1)
                    except Exception as refresh_error:
                        log_warning(f"页面刷新失败: {str(refresh_error)}", "密码修改")

            except Exception as e:
                log_error("密码修改", e)
                error_msg = f"密码修改尝试 {attempt + 1} 异常: {str(e)}"

                # 如果是最后一次尝试，返回失败
                if attempt == max_retries - 1:
                    return False, error_msg

                log_warning(f"{error_msg}，准备重试", "密码修改")
                time.sleep(self.retry_delay)

        # 所有重试都失败
        final_error = f"密码修改失败，已重试 {max_retries} 次"
        log_error("密码修改", Exception(final_error))
        return False, final_error

    def _attempt_password_change(self, page: Page, original_password: str) -> Tuple[bool, str]:
        """
        单次密码修改尝试

        Args:
            page: Playwright页面对象
            original_password: 原密码

        Returns:
            (是否处理成功, 处理结果描述)
        """
        try:
            # 步骤1: 填写原密码
            success, error_msg = self._fill_old_password(page, original_password)
            if not success:
                return False, f"填写原密码失败: {error_msg}"

            # 步骤2: 填写新密码（与原密码相同）
            success, error_msg = self._fill_new_password(page, original_password)
            if not success:
                return False, f"填写新密码失败: {error_msg}"

            # 步骤3: 填写确认密码
            success, error_msg = self._fill_confirm_password(page, original_password)
            if not success:
                return False, f"填写确认密码失败: {error_msg}"

            # 步骤4: 点击修改密码按钮
            success, error_msg = self._click_change_button(page)
            if not success:
                return False, f"点击修改按钮失败: {error_msg}"

            # 步骤5: 等待处理结果
            success, error_msg = self._wait_for_change_result(page)
            if not success:
                return False, f"密码修改处理失败: {error_msg}"

            return True, "密码修改流程处理成功"

        except Exception as e:
            log_error("密码修改", e)
            return False, f"处理密码修改流程异常: {str(e)}"

    def _fill_old_password(self, page: Page, password: str) -> Tuple[bool, str]:
        """填写原密码"""
        log_info("填写原密码", "密码修改")

        old_password_selectors = SELECTORS.get('old_password_input', [])

        for selector in old_password_selectors:
            try:
                # 使用较短的超时时间，避免长时间等待
                element = page.wait_for_selector(selector, timeout=min(self.element_timeout, 5000))
                if element:
                    # 确保元素可见和可交互
                    if not element.is_visible():
                        log_warning(f"原密码输入框不可见: {selector}", "密码修改")
                        continue

                    if not element.is_enabled():
                        log_warning(f"原密码输入框不可用: {selector}", "密码修改")
                        continue

                    # 清空输入框并填写密码
                    element.clear()
                    time.sleep(0.2)  # 短暂等待清空完成
                    element.fill(password)
                    time.sleep(0.2)  # 短暂等待填写完成

                    # 验证输入是否成功
                    try:
                        input_value = element.input_value()
                        if input_value == password:
                            log_success(f"原密码填写成功: {selector}", "密码修改")
                            return True, "原密码填写成功"
                        else:
                            log_warning(f"原密码填写验证失败: {selector}", "密码修改")
                            continue
                    except:
                        # 某些密码框可能无法读取值，直接认为成功
                        log_success(f"原密码填写成功: {selector}", "密码修改")
                        return True, "原密码填写成功"

            except Exception as e:
                log_warning(f"原密码选择器失败: {selector} - {str(e)}", "密码修改")
                continue

        return False, "未找到原密码输入框"

    def _fill_new_password(self, page: Page, password: str) -> Tuple[bool, str]:
        """填写新密码"""
        log_info("填写新密码", "密码修改")

        new_password_selectors = SELECTORS.get('new_password_input', [])

        for selector in new_password_selectors:
            try:
                # 使用较短的超时时间，避免长时间等待
                element = page.wait_for_selector(selector, timeout=min(self.element_timeout, 5000))
                if element:
                    # 确保元素可见和可交互
                    if not element.is_visible():
                        log_warning(f"新密码输入框不可见: {selector}", "密码修改")
                        continue

                    if not element.is_enabled():
                        log_warning(f"新密码输入框不可用: {selector}", "密码修改")
                        continue

                    # 清空输入框并填写密码
                    element.clear()
                    time.sleep(0.2)  # 短暂等待清空完成
                    element.fill(password)
                    time.sleep(0.2)  # 短暂等待填写完成

                    # 验证输入是否成功
                    try:
                        input_value = element.input_value()
                        if input_value == password:
                            log_success(f"新密码填写成功: {selector}", "密码修改")
                            return True, "新密码填写成功"
                        else:
                            log_warning(f"新密码填写验证失败: {selector}", "密码修改")
                            continue
                    except:
                        # 某些密码框可能无法读取值，直接认为成功
                        log_success(f"新密码填写成功: {selector}", "密码修改")
                        return True, "新密码填写成功"

            except Exception as e:
                log_warning(f"新密码选择器失败: {selector} - {str(e)}", "密码修改")
                continue

        return False, "未找到新密码输入框"

    def _fill_confirm_password(self, page: Page, password: str) -> Tuple[bool, str]:
        """填写确认密码"""
        log_info("填写确认密码", "密码修改")

        confirm_password_selectors = SELECTORS.get('confirm_password_input', [])

        for selector in confirm_password_selectors:
            try:
                # 使用较短的超时时间，避免长时间等待
                element = page.wait_for_selector(selector, timeout=min(self.element_timeout, 5000))
                if element:
                    # 确保元素可见和可交互
                    if not element.is_visible():
                        log_warning(f"确认密码输入框不可见: {selector}", "密码修改")
                        continue

                    if not element.is_enabled():
                        log_warning(f"确认密码输入框不可用: {selector}", "密码修改")
                        continue

                    # 清空输入框并填写密码
                    element.clear()
                    time.sleep(0.2)  # 短暂等待清空完成
                    element.fill(password)
                    time.sleep(0.2)  # 短暂等待填写完成

                    # 验证输入是否成功
                    try:
                        input_value = element.input_value()
                        if input_value == password:
                            log_success(f"确认密码填写成功: {selector}", "密码修改")
                            return True, "确认密码填写成功"
                        else:
                            log_warning(f"确认密码填写验证失败: {selector}", "密码修改")
                            continue
                    except:
                        # 某些密码框可能无法读取值，直接认为成功
                        log_success(f"确认密码填写成功: {selector}", "密码修改")
                        return True, "确认密码填写成功"

            except Exception as e:
                log_warning(f"确认密码选择器失败: {selector} - {str(e)}", "密码修改")
                continue

        return False, "未找到确认密码输入框"

    def _click_change_button(self, page: Page) -> Tuple[bool, str]:
        """点击修改密码按钮"""
        log_info("点击修改密码按钮", "密码修改")

        change_button_selectors = SELECTORS.get('change_password_button', [])

        for selector in change_button_selectors:
            try:
                # 使用较短的超时时间，避免长时间等待
                element = page.wait_for_selector(selector, timeout=min(self.element_timeout, 5000))
                if element:
                    # 确保元素可见和可交互
                    if not element.is_visible():
                        log_warning(f"修改密码按钮不可见: {selector}", "密码修改")
                        continue

                    if not element.is_enabled():
                        log_warning(f"修改密码按钮不可用: {selector}", "密码修改")
                        continue

                    # 点击按钮
                    element.click()
                    time.sleep(0.5)  # 等待点击响应

                    log_success(f"修改密码按钮点击成功: {selector}", "密码修改")
                    return True, "修改密码按钮点击成功"

            except Exception as e:
                log_warning(f"修改密码按钮选择器失败: {selector} - {str(e)}", "密码修改")
                continue

        return False, "未找到修改密码按钮"

    def _wait_for_change_result(self, page: Page) -> Tuple[bool, str]:
        """等待密码修改结果"""
        log_info("等待密码修改结果", "密码修改")

        try:
            # 等待页面响应，使用较短的超时时间
            try:
                page.wait_for_load_state("networkidle", timeout=min(self.page_timeout, 15000))
            except Exception as timeout_error:
                log_warning(f"等待页面稳定超时: {str(timeout_error)}", "密码修改")
                # 继续处理，不因为超时而失败

            time.sleep(2)

            current_url = page.url
            page_title = page.title()

            log_info(f"密码修改后URL: {current_url}", "密码修改")
            log_info(f"密码修改后标题: {page_title}", "密码修改")

            # 获取页面内容，但限制大小避免内存问题
            try:
                page_content = page.content()
                # 限制内容长度，避免处理过大的页面
                if len(page_content) > 50000:
                    page_content = page_content[:50000]
            except Exception as content_error:
                log_warning(f"获取页面内容失败: {str(content_error)}", "密码修改")
                page_content = ""

            # 检查成功指标
            success_indicators = [
                "成功", "修改成功", "密码修改成功", "操作成功", "完成",
                "success", "successful", "complete", "done", "ok"
            ]

            # 检查错误指标
            error_indicators = [
                "错误", "失败", "修改失败", "密码错误", "操作失败", "异常",
                "error", "fail", "failed", "invalid", "wrong", "exception"
            ]

            # 检查成功指标
            for indicator in success_indicators:
                if indicator in page_content.lower() or indicator in page_title.lower():
                    return True, f"密码修改成功，检测到成功指标: {indicator}"

            # 检查错误指标
            for indicator in error_indicators:
                if indicator in page_content.lower() or indicator in page_title.lower():
                    return False, f"密码修改失败，检测到错误指标: {indicator}"

            # 如果页面跳转了，认为可能成功
            if "password" not in current_url.lower() and "修改" not in current_url:
                return True, "密码修改可能成功，页面已跳转"

            # 检查是否还在密码修改页面
            if any(keyword in current_url.lower() for keyword in ["password", "修改", "change"]):
                return False, "仍在密码修改页面，可能修改失败"

            # 默认认为成功（保守策略）
            return True, "密码修改状态不明确，默认认为成功"

        except Exception as e:
            log_error("密码修改", e)
            return False, f"等待密码修改结果异常: {str(e)}"
