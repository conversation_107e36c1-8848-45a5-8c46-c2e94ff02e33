#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
自考成绩查询自动化系统 - 适配students1.csv格式
专门为您的学员数据格式定制
"""

import csv
import time
import argparse
from pathlib import Path
from datetime import datetime
from playwright.sync_api import sync_playwright

# 配置
CONFIG = {
    "login_url": "https://121.204.170.198:8082/zk/online/2/",
    "headless": False,  # 有头模式
    "slow_mo": 500,
    "timeout": 30000,
    "screenshot_dir": Path("screenshots"),
    "max_retries": 3
}

class AutoQuerySystem:
    """自动查询系统 - 适配students1.csv格式"""
    
    def __init__(self):
        self.results = {"total": 0, "success": 0, "failed": 0, "details": []}
        CONFIG["screenshot_dir"].mkdir(exist_ok=True)
    
    def load_students(self, filename="students1.csv"):
        """加载学员数据 - 适配您的CSV格式"""
        try:
            students = []
            with open(filename, 'r', encoding='utf-8') as f:
                reader = csv.DictReader(f)
                for row in reader:
                    # 检查必要字段并清理数据
                    name = row.get('姓名', '').strip()
                    id_number = row.get('身份证号', '').strip()
                    username = row.get('用户名', '').strip()
                    password = row.get('密码', '').strip()
                    
                    # 跳过空行或无效数据
                    if not name or not id_number or not password:
                        continue
                    
                    # 使用用户名作为登录名（您的数据中用户名就是身份证号）
                    login_username = username if username else id_number
                    
                    students.append({
                        '姓名': name,
                        '身份证号': id_number,
                        '用户名': login_username,
                        '密码': password
                    })
            
            print(f"✅ 成功加载 {len(students)} 名学员数据")
            return students
        except Exception as e:
            print(f"❌ 数据加载失败: {e}")
            return []
    
    def process_student(self, page, student, index, total):
        """处理单个学员"""
        name = student['姓名']
        username = student['用户名']  # 身份证号作为用户名
        password = student['密码']
        id_number = student['身份证号']
        
        print(f"\n📝 处理第 {index}/{total} 名学员: {name}")
        print(f"   身份证号: {id_number}")
        print(f"   登录用户名: {username}")
        
        try:
            # 步骤1: 导航到登录页面
            print("   🔗 导航到登录页面...")
            page.goto(CONFIG["login_url"])
            page.wait_for_load_state("networkidle")
            time.sleep(2)
            
            # 步骤2: 填写登录信息
            print("   📝 填写登录信息...")
            
            # 多种选择器尝试
            username_selectors = [
                "input[name='username']", "input[name='user']", 
                "#username", "#user", "input[type='text']"
            ]
            password_selectors = [
                "input[name='password']", "input[name='pass']", 
                "#password", "#pass", "input[type='password']"
            ]
            
            # 填写用户名
            username_filled = False
            for selector in username_selectors:
                try:
                    if page.locator(selector).count() > 0:
                        page.fill(selector, username)
                        username_filled = True
                        print(f"   ✅ 用户名已填写: {username}")
                        break
                except:
                    continue
            
            if not username_filled:
                print("   ❌ 未找到用户名输入框")
                return False
            
            # 填写密码
            password_filled = False
            for selector in password_selectors:
                try:
                    if page.locator(selector).count() > 0:
                        page.fill(selector, password)
                        password_filled = True
                        print("   ✅ 密码已填写")
                        break
                except:
                    continue
            
            if not password_filled:
                print("   ❌ 未找到密码输入框")
                return False
            
            # 步骤3: 处理验证码
            print("   🔍 处理验证码...")
            captcha_selectors = [
                "input[name='captcha']", "input[name='code']", 
                "#captcha", "#code", "input[name='validateCode']"
            ]
            
            # 查找验证码输入框
            captcha_input = None
            for selector in captcha_selectors:
                try:
                    if page.locator(selector).count() > 0:
                        captcha_input = selector
                        break
                except:
                    continue
            
            if captcha_input:
                # 截图验证码区域供用户查看
                temp_screenshot = f"temp_captcha_{name}.png"
                page.screenshot(path=temp_screenshot)
                print(f"   📸 验证码截图已保存: {temp_screenshot}")
                
                captcha_code = input(f"   ⌨️ 请查看验证码图片并输入验证码: ")
                page.fill(captcha_input, captcha_code)
                print("   ✅ 验证码已填写")
            else:
                print("   ⚠️ 未找到验证码输入框，可能不需要验证码")
            
            # 步骤4: 点击登录
            print("   🔐 执行登录...")
            login_selectors = [
                "input[type='submit']", "button[type='submit']",
                "input[value*='登录']", "button:has-text('登录')",
                ".login-btn", "#login", "input[value='登录']"
            ]
            
            login_clicked = False
            for selector in login_selectors:
                try:
                    if page.locator(selector).count() > 0:
                        page.click(selector)
                        login_clicked = True
                        print("   ✅ 登录按钮已点击")
                        break
                except:
                    continue
            
            if not login_clicked:
                print("   ❌ 未找到登录按钮")
                return False
            
            # 等待登录结果
            print("   ⏳ 等待登录结果...")
            page.wait_for_load_state("networkidle")
            time.sleep(3)
            
            # 检查是否登录成功
            current_url = page.url
            if "login" in current_url.lower():
                print("   ❌ 登录失败，仍在登录页面")
                return False
            
            print("   ✅ 登录成功")
            
            # 步骤5: 查找成绩查询链接
            print("   🧭 查找成绩查询链接...")
            score_selectors = [
                "text=当次成绩查询", "text=成绩查询", "text=查询成绩",
                "//a[contains(text(), '当次成绩查询')]",
                "//a[contains(text(), '成绩查询')]",
                "//a[contains(text(), '成绩')]"
            ]
            
            found_link = False
            for selector in score_selectors:
                try:
                    if page.locator(selector).count() > 0:
                        page.click(selector)
                        page.wait_for_load_state("networkidle")
                        found_link = True
                        print(f"   ✅ 找到并点击成绩查询链接")
                        break
                except:
                    continue
            
            if not found_link:
                print("   ❌ 未找到成绩查询链接")
                # 截图当前页面供调试
                debug_screenshot = f"debug_{name}_no_score_link.png"
                page.screenshot(path=debug_screenshot)
                print(f"   📸 调试截图已保存: {debug_screenshot}")
                return False
            
            # 步骤6: 处理查询选项
            print("   🎯 处理查询选项...")
            time.sleep(2)  # 等待页面加载
            
            option_selectors = [
                "text=点击进入", "//a[contains(text(), '点击进入')]",
                "input[value='点击进入']", "button:has-text('点击进入')",
                "//input[@type='submit']", "//button[@type='submit']"
            ]
            
            options = []
            for selector in option_selectors:
                try:
                    elements = page.locator(selector)
                    for i in range(elements.count()):
                        options.append(elements.nth(i))
                except:
                    continue
            
            if options:
                # 智能选择：多个选第二个，单个选第一个
                if len(options) == 1:
                    selected_option = options[0]
                    print(f"   📌 单个选项，选择第一个")
                else:
                    selected_option = options[1] if len(options) > 1 else options[0]
                    print(f"   📌 多个选项({len(options)}个)，选择第二个")
                
                selected_option.click()
                page.wait_for_load_state("networkidle")
                time.sleep(2)
                print("   ✅ 查询选项已选择")
            else:
                print("   ⚠️ 未找到查询选项，可能已经在成绩页面")
            
            # 步骤7: 截图成绩单
            print("   📸 截图成绩单...")
            screenshot_path = CONFIG["screenshot_dir"] / f"{name}.png"
            
            # 尝试定位成绩表格
            table_selectors = [
                "//table[.//td[contains(text(), '成绩')]]",
                "//table[.//td[contains(text(), '科目')]]",
                "//table[.//td[contains(text(), '分数')]]",
                "//table[count(.//tr) > 1]",  # 有多行的表格
                "table"
            ]
            
            screenshot_success = False
            for selector in table_selectors:
                try:
                    if page.locator(selector).count() > 0:
                        table = page.locator(selector).first
                        table.screenshot(path=str(screenshot_path))
                        screenshot_success = True
                        print(f"   ✅ 表格截图成功")
                        break
                except:
                    continue
            
            # 如果表格截图失败，截取整个页面
            if not screenshot_success:
                page.screenshot(path=str(screenshot_path), full_page=True)
                print(f"   ✅ 全页面截图成功")
            
            print(f"   📁 截图保存: {screenshot_path}")
            
            # 清理临时验证码截图
            try:
                temp_file = Path(f"temp_captcha_{name}.png")
                if temp_file.exists():
                    temp_file.unlink()
            except:
                pass
            
            return True

        except Exception as e:
            print(f"   ❌ 处理失败: {e}")
            return False

    def run(self, data_file="students1.csv"):
        """运行主程序"""
        print("🎯 自考成绩查询自动化系统")
        print("=" * 50)

        # 加载学员数据
        students = self.load_students(data_file)
        if not students:
            return False

        self.results["total"] = len(students)

        # 显示学员列表
        print(f"\n📋 学员列表 ({len(students)} 名):")
        for i, student in enumerate(students, 1):
            print(f"   {i:2d}. {student['姓名']} ({student['身份证号']})")

        # 确认开始处理
        print(f"\n⚠️ 即将开始批量处理 {len(students)} 名学员")
        print("   系统将使用有头模式运行，您可以看到浏览器操作过程")
        confirm = input("是否继续？(y/N): ").strip().lower()
        if confirm not in ['y', 'yes']:
            print("❌ 用户取消操作")
            return False

        # 启动浏览器处理
        print(f"\n🚀 开始批量处理...")
        start_time = datetime.now()

        with sync_playwright() as p:
            browser = p.chromium.launch(
                headless=CONFIG["headless"],
                slow_mo=CONFIG["slow_mo"]
            )
            page = browser.new_page()
            page.set_default_timeout(CONFIG["timeout"])

            for i, student in enumerate(students, 1):
                print(f"\n{'='*60}")

                # 显示进度
                progress = (i / len(students)) * 100
                print(f"📊 进度: {progress:.1f}% ({i}/{len(students)})")

                # 处理学员
                start_student_time = time.time()
                success = self.process_student(page, student, i, len(students))
                process_time = time.time() - start_student_time

                # 记录结果
                if success:
                    self.results["success"] += 1
                    print(f"✅ 处理成功 (耗时: {process_time:.1f}秒)")
                else:
                    self.results["failed"] += 1
                    print(f"❌ 处理失败 (耗时: {process_time:.1f}秒)")

                # 处理间隔
                if i < len(students):
                    print("⏳ 等待3秒后处理下一名学员...")
                    time.sleep(3)

            browser.close()

        # 显示最终结果
        end_time = datetime.now()
        total_time = end_time - start_time
        success_rate = (self.results["success"] / self.results["total"]) * 100

        print(f"\n{'='*60}")
        print("📊 批量处理结果统计")
        print(f"{'='*60}")
        print(f"处理时间: {start_time.strftime('%H:%M:%S')} - {end_time.strftime('%H:%M:%S')}")
        print(f"总耗时: {total_time}")
        print(f"处理总数: {self.results['total']} 名学员")
        print(f"成功数量: {self.results['success']} 名")
        print(f"失败数量: {self.results['failed']} 名")
        print(f"成功率: {success_rate:.1f}%")

        print(f"\n📸 截图文件保存在: {CONFIG['screenshot_dir']}")
        screenshots = list(CONFIG["screenshot_dir"].glob("*.png"))
        print(f"📁 生成截图: {len(screenshots)} 个文件")

        return success_rate >= 70  # 70%成功率即可

def main():
    """主函数"""
    parser = argparse.ArgumentParser(description="自考成绩查询自动化系统 - students1.csv版本")
    parser.add_argument("--data", "-d", default="students1.csv", help="学员数据文件")
    parser.add_argument("--headless", action="store_true", help="无头模式")

    args = parser.parse_args()

    if args.headless:
        CONFIG["headless"] = True
        print("⚠️ 启用无头模式，浏览器将在后台运行")
    else:
        print("✅ 使用有头模式，您可以看到浏览器操作过程")

    system = AutoQuerySystem()
    success = system.run(args.data)

    if success:
        print("\n🎉 系统运行完成！")
    else:
        print("\n⚠️ 系统运行遇到问题。")

if __name__ == "__main__":
    main()
