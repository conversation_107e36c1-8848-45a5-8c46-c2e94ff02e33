#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
MarkItDown 演示脚本
演示如何使用MarkItDown Python API转换各种文件格式
"""

from markitdown import MarkItDown
import os

def demo_markitdown():
    """演示MarkItDown的基本功能"""
    print("🚀 MarkItDown 演示开始")
    print("=" * 50)
    
    # 初始化MarkItDown（禁用插件）
    md = MarkItDown(enable_plugins=False)
    
    # 创建一个简单的HTML测试文件
    html_content = """
    <!DOCTYPE html>
    <html>
    <head>
        <title>测试文档</title>
    </head>
    <body>
        <h1>这是一个标题</h1>
        <p>这是一个段落，包含<strong>粗体</strong>和<em>斜体</em>文本。</p>
        <ul>
            <li>列表项目1</li>
            <li>列表项目2</li>
            <li>列表项目3</li>
        </ul>
        <table>
            <tr>
                <th>姓名</th>
                <th>年龄</th>
            </tr>
            <tr>
                <td>张三</td>
                <td>25</td>
            </tr>
            <tr>
                <td>李四</td>
                <td>30</td>
            </tr>
        </table>
    </body>
    </html>
    """
    
    # 保存HTML文件
    with open("test.html", "w", encoding="utf-8") as f:
        f.write(html_content)
    
    print("📝 已创建测试HTML文件: test.html")
    
    # 转换HTML文件
    print("\n🔄 转换HTML文件...")
    result = md.convert("test.html")
    
    print("\n📄 转换结果:")
    print("-" * 30)
    print(result.text_content)
    print("-" * 30)
    
    # 保存Markdown结果
    with open("test_output.md", "w", encoding="utf-8") as f:
        f.write(result.text_content)
    
    print("💾 已保存Markdown文件: test_output.md")
    
    # 演示文本内容转换
    print("\n🔄 演示文本内容转换...")
    text_content = "这是一个**测试**文本，包含`代码`和[链接](https://example.com)"
    
    # 创建临时文本文件
    with open("test.txt", "w", encoding="utf-8") as f:
        f.write(text_content)
    
    result2 = md.convert("test.txt")
    print(f"文本转换结果: {result2.text_content}")
    
    print("\n✅ 演示完成！")
    print("🎯 支持的文件格式包括: PDF, Word, Excel, PowerPoint, 图片, 音频, HTML, CSV, JSON, XML等")

if __name__ == "__main__":
    demo_markitdown()