#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试students1.csv数据加载
"""

import csv

def test_load_students():
    """测试加载学员数据"""
    print("🧪 测试加载students1.csv数据...")
    
    try:
        students = []
        with open("students1.csv", 'r', encoding='utf-8') as f:
            reader = csv.DictReader(f)
            for row in reader:
                # 检查必要字段并清理数据
                name = row.get('姓名', '').strip()
                id_number = row.get('身份证号', '').strip()
                username = row.get('用户名', '').strip()
                password = row.get('密码', '').strip()
                
                # 跳过空行或无效数据
                if not name or not id_number or not password:
                    continue
                
                # 使用用户名作为登录名，如果没有则使用身份证号
                login_username = username if username else id_number
                
                students.append({
                    '姓名': name,
                    '身份证号': id_number,
                    '用户名': login_username,
                    '密码': password
                })
        
        print(f"✅ 成功加载 {len(students)} 名学员数据")
        
        # 显示学员信息
        print("\n📋 学员列表:")
        for i, student in enumerate(students, 1):
            print(f"   {i:2d}. {student['姓名']} ({student['身份证号']}) - 登录名: {student['用户名']}")
        
        return True
    except Exception as e:
        print(f"❌ 数据加载失败: {e}")
        return False

def test_dependencies():
    """测试依赖包"""
    print("\n📦 测试依赖包...")
    
    try:
        from playwright.sync_api import sync_playwright
        print("✅ Playwright - 已安装")
        return True
    except ImportError as e:
        print(f"❌ Playwright 未安装: {e}")
        return False

if __name__ == "__main__":
    print("🔍 students1.csv 数据测试")
    print("=" * 30)
    
    success1 = test_load_students()
    success2 = test_dependencies()
    
    if success1 and success2:
        print("\n🎉 测试通过！可以运行主程序")
        print("\n🚀 使用方法:")
        print("   python 自考成绩查询2/main_students1.py")
    else:
        print("\n⚠️ 测试未通过，请检查问题")
