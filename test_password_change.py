#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试密码修改功能
验证更新后的选择器是否能正确识别页面元素
"""

import sys
import time
from pathlib import Path
from playwright.sync_api import sync_playwright

# 添加src目录到Python路径
sys.path.append(str(Path(__file__).parent / "src"))

from config import CONFIG
from modules.logger import get_logger_manager, log_info, log_success, log_error, log_warning

def test_password_change_selectors():
    """测试密码修改页面的选择器"""
    
    logger_manager = get_logger_manager()
    log_info("开始测试密码修改选择器", "测试")
    
    with sync_playwright() as p:
        # 启动浏览器
        browser = p.chromium.launch(
            headless=False,
            slow_mo=500
        )
        
        try:
            context = browser.new_context(
                viewport={'width': 1920, 'height': 1080}
            )
            page = context.new_page()
            
            # 导航到密码修改页面（这是我们从浏览器看到的实际URL）
            password_change_url = "https://121.204.170.198:8082/zk/online/2/?p=uRegInfo&a=lmtpwd&r=1-WEB83-366-7c2-1173489-1753687560-U"
            
            log_info(f"导航到密码修改页面: {password_change_url}", "测试")
            page.goto(password_change_url, timeout=30000)
            
            # 等待页面加载
            time.sleep(2)
            
            # 测试选择器
            selectors = CONFIG['selectors']
            
            # 测试新密码输入框
            log_info("测试新密码输入框选择器", "测试")
            new_password_found = False
            for selector in selectors['new_password_input']:
                try:
                    element = page.query_selector(selector)
                    if element:
                        log_success(f"新密码输入框选择器有效: {selector}", "测试")
                        new_password_found = True
                        break
                except Exception as e:
                    continue
            
            if not new_password_found:
                log_error("未找到新密码输入框", "测试")
            
            # 测试确认密码输入框
            log_info("测试确认密码输入框选择器", "测试")
            confirm_password_found = False
            for selector in selectors['confirm_password_input']:
                try:
                    element = page.query_selector(selector)
                    if element:
                        log_success(f"确认密码输入框选择器有效: {selector}", "测试")
                        confirm_password_found = True
                        break
                except Exception as e:
                    continue
            
            if not confirm_password_found:
                log_error("未找到确认密码输入框", "测试")
            
            # 测试提交按钮
            log_info("测试提交按钮选择器", "测试")
            submit_button_found = False
            for selector in selectors['change_password_button']:
                try:
                    element = page.query_selector(selector)
                    if element:
                        log_success(f"提交按钮选择器有效: {selector}", "测试")
                        submit_button_found = True
                        break
                except Exception as e:
                    continue
            
            if not submit_button_found:
                log_error("未找到提交按钮", "测试")
            
            # 总结测试结果
            if new_password_found and confirm_password_found and submit_button_found:
                log_success("所有密码修改选择器测试通过！", "测试")
                return True
            else:
                log_error("部分选择器测试失败", "测试")
                return False
                
        except Exception as e:
            log_error(f"测试过程中发生错误: {str(e)}", "测试")
            return False
            
        finally:
            browser.close()

if __name__ == "__main__":
    success = test_password_change_selectors()
    if success:
        print("\n✅ 密码修改选择器测试成功！")
    else:
        print("\n❌ 密码修改选择器测试失败！")
