#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
登录模块
实现自动登录功能，包括验证码处理
"""

import time
from typing import <PERSON><PERSON>, Optional
from playwright.sync_api import Page

import sys
sys.path.append('..')
from config import CONFIG, SELECTORS
from modules.logger import log_info, log_success, log_error, log_warning
from modules.browser import BrowserManager
from modules.captcha import CaptchaRecognizer
from modules.password_change import PasswordChangeHandler


class LoginManager:
    """登录管理器"""

    def __init__(self, browser_manager: Optional[BrowserManager] = None):
        """
        初始化登录管理器

        Args:
            browser_manager: 可选的浏览器管理器实例，如果不提供则会创建新的
        """
        self.browser_manager = browser_manager
        self.external_browser = browser_manager is not None
        self.captcha_recognizer = CaptchaRecognizer()
        self.password_change_handler = PasswordChangeHandler()
    
    def perform_login(self, username: str, password: str, max_retries: int = 3) -> <PERSON><PERSON>[bool, str, Optional[Page]]:
        """
        执行登录操作

        Args:
            username: 用户名
            password: 密码
            max_retries: 最大重试次数

        Returns:
            (是否成功, 错误信息, 页面对象)
        """
        log_info(f"开始登录流程: {username}", "登录")

        # 如果没有传入浏览器实例，则创建新的
        if not self.browser_manager:
            self.browser_manager = BrowserManager()
            if not self.browser_manager.init_browser():
                return False, "浏览器初始化失败", None

            # 导航到登录页面
            if not self.browser_manager.navigate_to_login():
                self.browser_manager.close()
                return False, "登录页面加载失败", None

            page = self.browser_manager.get_page()
        else:
            # 使用传入的浏览器实例创建新页面
            page = self.browser_manager.new_page()
            if not page:
                return False, "无法创建新页面", None

            # 导航到登录页面
            try:
                log_info(f"导航到登录页面: {CONFIG['login_url']}", "浏览器")
                response = page.goto(CONFIG["login_url"])

                if response and response.status == 200:
                    log_success("登录页面加载成功", "浏览器")
                    # 等待页面稳定
                    page.wait_for_load_state("networkidle")
                    time.sleep(2)
                else:
                    log_warning(f"登录页面响应异常: {response.status if response else 'None'}", "浏览器")
                    self.browser_manager.close_page(page)
                    return False, "登录页面加载失败", None
            except Exception as e:
                log_error("浏览器", e)
                self.browser_manager.close_page(page)
                return False, "登录页面导航失败", None
        
        # 尝试登录
        for attempt in range(max_retries):
            log_info(f"登录尝试 {attempt + 1}/{max_retries}", "登录")

            success, error_msg = self._attempt_login(page, username, password)

            if success:
                log_success("登录成功", "登录")
                return True, "登录成功", page

            log_warning(f"登录失败: {error_msg}", "登录")

            if attempt < max_retries - 1:
                log_info("准备重试登录", "登录")
                # 刷新页面重试
                if self.external_browser:
                    # 使用外部浏览器时，重新加载页面
                    try:
                        page.reload()
                        page.wait_for_load_state("networkidle")
                        time.sleep(2)
                    except Exception as e:
                        log_error("浏览器", e)
                        break
                else:
                    # 使用内部浏览器时，使用原有方法
                    self.browser_manager.reload_page()
                    time.sleep(2)

        # 所有尝试都失败，根据浏览器来源决定清理策略
        if self.external_browser:
            # 外部浏览器只关闭页面
            self.browser_manager.close_page(page)
        else:
            # 内部浏览器关闭整个浏览器
            self.browser_manager.close()

        return False, f"登录失败，已重试{max_retries}次", None
    
    def _attempt_login(self, page: Page, username: str, password: str) -> Tuple[bool, str]:
        """
        单次登录尝试
        
        Args:
            page: 页面对象
            username: 用户名
            password: 密码
            
        Returns:
            (是否成功, 错误信息)
        """
        try:
            # 查找并填写用户名
            if not self._fill_username(page, username):
                return False, "用户名输入失败"
            
            # 查找并填写密码
            if not self._fill_password(page, password):
                return False, "密码输入失败"
            
            # 处理验证码
            if not self._handle_captcha(page):
                return False, "验证码处理失败"
            
            # 点击登录按钮
            if not self._click_login_button(page):
                return False, "登录按钮点击失败"
            
            # 等待登录结果
            return self._wait_for_login_result_with_password_change(page, password)
            
        except Exception as e:
            log_error("登录", e)
            return False, f"登录过程异常: {str(e)}"
    
    def _fill_username(self, page: Page, username: str) -> bool:
        """填写用户名"""
        log_info("填写用户名", "登录")
        
        for selector in SELECTORS["username_input"]:
            try:
                elements = page.locator(selector)
                if elements.count() > 0:
                    element = elements.first
                    element.clear()
                    element.fill(username)
                    
                    # 验证输入
                    if element.input_value() == username:
                        log_success(f"用户名输入成功: {selector}", "登录")
                        return True
                    
            except Exception as e:
                log_warning(f"用户名输入失败 {selector}: {e}", "登录")
                continue
        
        log_error("登录", Exception("未找到用户名输入框"))
        return False
    
    def _fill_password(self, page: Page, password: str) -> bool:
        """填写密码"""
        log_info("填写密码", "登录")
        
        for selector in SELECTORS["password_input"]:
            try:
                elements = page.locator(selector)
                if elements.count() > 0:
                    element = elements.first
                    element.clear()
                    element.fill(password)
                    
                    # 验证输入（密码框可能不返回值）
                    log_success(f"密码输入成功: {selector}", "登录")
                    return True
                    
            except Exception as e:
                log_warning(f"密码输入失败 {selector}: {e}", "登录")
                continue
        
        log_error("登录", Exception("未找到密码输入框"))
        return False
    
    def _handle_captcha(self, page: Page) -> bool:
        """处理验证码"""
        log_info("开始处理验证码", "登录")
        
        # 检查是否存在验证码
        captcha_image, captcha_input = self.captcha_recognizer.find_captcha_elements(page)
        
        if not captcha_image or not captcha_input:
            log_info("未检测到验证码，跳过验证码处理", "登录")
            return True
        
        # 处理验证码
        return self.captcha_recognizer.handle_captcha_with_retry(page, CONFIG["captcha_retries"])
    
    def _click_login_button(self, page: Page) -> bool:
        """点击登录按钮"""
        log_info("点击登录按钮", "登录")
        
        for selector in SELECTORS["login_button"]:
            try:
                elements = page.locator(selector)
                if elements.count() > 0:
                    element = elements.first
                    element.click()
                    log_success(f"登录按钮点击成功: {selector}", "登录")
                    return True
                    
            except Exception as e:
                log_warning(f"登录按钮点击失败 {selector}: {e}", "登录")
                continue
        
        log_error("登录", Exception("未找到登录按钮"))
        return False

    def _wait_for_login_result_with_password_change(self, page: Page, password: str) -> Tuple[bool, str]:
        """等待登录结果并处理密码修改"""
        log_info("等待登录结果（包含密码修改检测）", "登录")

        try:
            # 等待页面跳转或错误信息
            page.wait_for_load_state("networkidle", timeout=10000)
            time.sleep(3)

            current_url = page.url
            page_title = page.title()
            page_content = page.content()

            log_info(f"登录后URL: {current_url}", "登录")
            log_info(f"登录后标题: {page_title}", "登录")

            # 首先检查是否有错误
            error_indicators = [
                "错误", "失败", "验证码", "用户名", "密码", "登录",
                "error", "fail", "captcha", "username", "password", "login"
            ]

            # 检查错误指标
            for indicator in error_indicators:
                if indicator in page_content.lower():
                    return False, f"登录失败，页面包含错误信息: {indicator}"

            # 检查是否还在登录页面
            if CONFIG["login_url"] in current_url:
                return False, "仍在登录页面，登录可能失败"

            # 到这里说明可能登录成功，检查是否有密码修改提示
            log_info("登录可能成功，检查是否需要修改密码", "登录")

            # 检测密码修改页面
            is_password_change_page, detection_msg = self.password_change_handler.detect_password_change_page(page)

            if is_password_change_page:
                log_info(f"检测到密码修改页面: {detection_msg}", "登录")

                # 处理密码修改
                try:
                    success, change_msg = self.password_change_handler.handle_password_change(page, password)
                    if success:
                        log_success(f"密码修改处理成功: {change_msg}", "登录")

                        # 密码修改后重新等待页面稳定
                        page.wait_for_load_state("networkidle", timeout=10000)
                        time.sleep(2)

                        # 重新检查登录状态
                        final_url = page.url
                        final_title = page.title()
                        log_info(f"密码修改后URL: {final_url}", "登录")
                        log_info(f"密码修改后标题: {final_title}", "登录")

                        return True, f"登录成功，已处理密码修改: {change_msg}"
                    else:
                        log_warning(f"密码修改处理失败: {change_msg}，但继续登录流程", "登录")
                        # 即使密码修改失败，也认为登录成功
                        return True, f"登录成功，密码修改处理失败: {change_msg}"

                except Exception as e:
                    log_error("登录", e)
                    log_warning(f"密码修改处理异常: {str(e)}，但继续登录流程", "登录")
                    # 即使密码修改异常，也认为登录成功
                    return True, f"登录成功，密码修改处理异常: {str(e)}"
            else:
                log_info(f"未检测到密码修改页面: {detection_msg}", "登录")

            # 检查登录成功指标
            success_indicators = [
                "主页", "首页", "欢迎", "成功", "查询", "成绩",
                "main", "index", "welcome", "success", "query", "grade"
            ]

            # 检查成功指标
            for indicator in success_indicators:
                if indicator in page_title.lower() or indicator in current_url.lower():
                    return True, "登录成功"

            # 如果页面已跳转且没有错误，认为登录成功
            return True, "登录成功，页面已跳转"

        except Exception as e:
            log_error("登录", e)
            return False, f"等待登录结果异常: {str(e)}"

    def _wait_for_login_result(self, page: Page) -> Tuple[bool, str]:
        """等待登录结果"""
        log_info("等待登录结果", "登录")

        try:
            # 等待页面跳转或错误信息
            page.wait_for_load_state("networkidle", timeout=10000)
            time.sleep(3)

            current_url = page.url
            page_title = page.title()
            page_content = page.content()

            log_info(f"登录后URL: {current_url}", "登录")
            log_info(f"登录后标题: {page_title}", "登录")

            # 首先检查是否有错误
            error_indicators = [
                "错误", "失败", "验证码", "用户名", "密码", "登录",
                "error", "fail", "captcha", "username", "password", "login"
            ]

            # 检查错误指标
            for indicator in error_indicators:
                if indicator in page_content.lower():
                    return False, f"登录失败，页面包含错误信息: {indicator}"

            # 检查是否还在登录页面
            if CONFIG["login_url"] in current_url:
                return False, "仍在登录页面，登录可能失败"

            # 到这里说明可能登录成功，检查是否有密码修改提示
            log_info("登录可能成功，检查是否需要修改密码", "登录")

            # 检测密码修改页面
            is_password_change_page, detection_msg = self.password_change_handler.detect_password_change_page(page)

            if is_password_change_page:
                log_info(f"检测到密码修改页面: {detection_msg}", "登录")

                # 处理密码修改（需要原密码，这里需要从上下文获取）
                # 注意：这里我们需要修改方法签名来传递原密码
                # 暂时跳过密码修改处理，只记录日志
                log_warning("检测到密码修改页面，但当前版本暂不处理，继续登录流程", "登录")

                # 即使有密码修改提示，也认为登录成功
                return True, f"登录成功，检测到密码修改提示: {detection_msg}"
            else:
                log_info(f"未检测到密码修改页面: {detection_msg}", "登录")

            # 检查登录成功指标
            success_indicators = [
                "主页", "首页", "欢迎", "成功", "查询", "成绩",
                "main", "index", "welcome", "success", "query", "grade"
            ]

            # 检查成功指标
            for indicator in success_indicators:
                if indicator in page_title.lower() or indicator in current_url.lower():
                    return True, "登录成功"

            # 如果页面已跳转且没有错误，认为登录成功
            return True, "登录成功，页面已跳转"

        except Exception as e:
            log_error("登录", e)
            return False, f"等待登录结果异常: {str(e)}"
    
    def close(self):
        """关闭登录管理器"""
        if self.browser_manager and not self.external_browser:
            # 只有当浏览器是内部创建的时候才关闭
            self.browser_manager.close()
            self.browser_manager = None


# 便捷函数
def perform_auto_login(username: str, password: str, max_retries: int = 3, browser_manager: Optional[BrowserManager] = None) -> Tuple[bool, str, Optional[Page]]:
    """
    自动登录的便捷函数

    Args:
        username: 用户名
        password: 密码
        max_retries: 最大重试次数
        browser_manager: 可选的浏览器管理器实例

    Returns:
        (是否成功, 错误信息, 页面对象)
    """
    login_manager = LoginManager(browser_manager)
    return login_manager.perform_login(username, password, max_retries)


def check_login_status(page: Page) -> bool:
    """
    检查登录状态
    
    Args:
        page: 页面对象
        
    Returns:
        是否已登录
    """
    try:
        current_url = page.url
        page_title = page.title()
        
        # 如果还在登录页面，说明未登录
        if CONFIG["login_url"] in current_url:
            return False
        
        # 检查页面标题是否包含登录成功的指标
        success_indicators = ["主页", "首页", "欢迎", "查询", "成绩"]
        for indicator in success_indicators:
            if indicator in page_title:
                return True
        
        return True  # 默认认为已登录
        
    except Exception as e:
        log_error("登录", e)
        return False
